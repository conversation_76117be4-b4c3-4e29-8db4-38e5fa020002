# -*- mode: python ; coding: utf-8 -*-
"""
ملف تحزيم بأقل حجم ممكن - نسخة محسنة للغاية
===============================================

آخر تحديث: 2025-08-28
التحسينات المطبقة:
- إزالة جميع الملفات غير الضرورية من datas
- تقليل hiddenimports للحد الأدنى
- استخدام onefile لملف واحد مضغوط
- قائمة استبعاد شاملة جداً
- تحسين إعدادات الضغط
"""

import os
block_cipher = None

# ملفات البيانات الأساسية فقط - الحد الأدنى المطلق
datas=[
    ('01.ico', '.'),
    ('data.db', '.'),
    ('fonts/Arial.ttf', 'fonts/'),
    ('fonts/arialbd.ttf', 'fonts/'),
]

a = Analysis(
    ['main_window.py'],
    pathex=[r'c:\Users\<USER>\Desktop\csv\taheri10'],
    binaries=[],
    datas=datas,
    hiddenimports=[
        # النوافذ الأساسية فقط
        'main_window',
        'sub01_window', 'sub2_window', 'sub8_window',
        'sub252_window', 'sub262_window', 'sub263_window', 'sub26662_window',
        'sub3_window', 'sub4_window', 'sub100_window',
        'expense_management_window', 'cash_flow_window', 'financial_system_launcher',
        'monthly_duties_window', 'attendance_processing_window',
        'student_multi_registration', 'run_student_registration', 'check_db_structure',
        
        # ملفات الطباعة الأساسية
        'print101', 'print111', 'print144',
        'print_registration_fees', 'print_registration_fees_monthly_style',
        'print_registration_fees_simple', 'print_registration_fees_all_sections',
        'print1_section_monthly', 'print_section_monthly', 'print_section_yearly',
        'attendance_sheet_report', 'daily_attendance_sheet_report',
        
        # المكتبات الأساسية فقط
        'fpdf', 'fpdf.fpdf',
        'arabic_reshaper', 'bidi', 'bidi.algorithm',
        'fontTools', 'fontTools.ttLib',
        'PyQt5.QtWidgets', 'PyQt5.QtGui', 'PyQt5.QtCore', 'PyQt5.sip',
        'sqlite3', '_sqlite3',
        'encodings.utf_8', 'encodings.cp1256',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        # استبعاد شامل لجميع المكتبات غير المستخدمة
        'matplotlib', 'numpy', 'pandas', 'scipy', 'sklearn', 'tensorflow', 'torch',
        'reportlab', 'PIL', 'Pillow', 'opencv', 'cv2',
        'cryptography', 'psutil', 'requests', 'urllib3', 'certifi',
        'xlsxwriter', 'xlrd', 'openpyxl', 'xlwt', 'xlutils',
        'dateutil', 'pytz', 'babel',
        'win32print', 'win32api', 'win32con', 'win32gui', 'win32file',
        'win32pipe', 'win32process', 'win32security', 'win32service',
        'pywintypes', 'pythoncom', 'win32com',
        'tkinter', 'turtle', 'curses', 'readline',
        'test', 'unittest', 'doctest', 'pdb', 'profile', 'pstats',
        'distutils', 'setuptools', 'pip', 'wheel', 'pkg_resources',
        'multiprocessing', 'concurrent', 'asyncio', 'threading', 'queue',
        'xml', 'html', 'email', 'http', 'urllib', 'ssl', 'socket',
        'pickle', 'shelve', 'dbm', 'sqlite3',
        'base64', 'binascii', 'uu', 'binhex', 'quopri',
        'gzip', 'bz2', 'lzma', 'zipfile', 'tarfile', 'zlib',
        'hashlib', 'hmac', 'secrets', 'uuid', 'random', 'statistics',
        'decimal', 'fractions', 'math', 'cmath', 'numbers',
        'array', 'struct', 'codecs', 'unicodedata', 'string', 're',
        'collections', 'itertools', 'functools', 'operator', 'weakref',
        'copy', 'pprint', 'textwrap', 'difflib', 'stringprep',
        'pathlib', 'glob', 'fnmatch', 'linecache', 'shutil', 'tempfile',
        'filecmp', 'stat', 'platform', 'ctypes', 'mmap', 'resource', 'gc',
        'sys', 'os', 'io', 'time', 'datetime', 'calendar', 'zoneinfo',
        'locale', 'gettext', 'argparse', 'getopt', 'logging', 'getpass',
        'subprocess', 'signal', 'atexit', 'traceback', 'warnings',
        'contextlib', 'abc', 'rlcompleter', 'keyword', 'token', 'tokenize',
        'ast', 'symtable', 'symbol', 'compiler', 'dis', 'pickletools',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

# ملف واحد مضغوط بأقل حجم ممكن
exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='المعين_في_الحراسة_العامة_PDF_أصغر_حجم',
    debug=False,
    bootloader_ignore_signals=False,
    strip=True,     # تفعيل strip لتقليل الحجم
    upx=False,      # إيقاف UPX لتجنب مشاكل مكافحة الفيروسات
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='01.ico',
)
