#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
تقرير القسم الشهري - محدث
==========================

التحديثات المطبقة:
- تم الاستغناء عن جدول البيانات المرحلة
- تم الاستغناء عن جدول المواد والأقسام
- الاعتماد على جدول monthly_duties فقط لجلب جميع البيانات
- حصة الأستاذ من المبلغ = عمود نسبة_الاستاذ من جدول monthly_duties
- إحصائيات الذكور والإناث تعتمد على عمود الجنس من جدول monthly_duties
"""

import os
import sys
import sqlite3
import traceback
from datetime import datetime

def get_database_path():
    """
    الحصول على مسار قاعدة البيانات الصحيح
    يعمل في البيئة العادية وبعد التحزيم
    """
    if getattr(sys, 'frozen', False):
        # البرنامج محزم - البحث في مجلد البرنامج الرئيسي
        application_path = os.path.dirname(sys.executable)
    else:
        # البرنامج يعمل من المصدر
        application_path = os.path.dirname(os.path.abspath(__file__))

    db_path = os.path.join(application_path, 'data.db')

    # التحقق من وجود قاعدة البيانات
    if not os.path.exists(db_path):
        # محاولة البحث في مجلد أعلى
        parent_path = os.path.dirname(application_path)
        alternative_db_path = os.path.join(parent_path, 'data.db')
        if os.path.exists(alternative_db_path):
            return alternative_db_path

    return db_path
import subprocess

# إعدادات التقرير
COL_WIDTHS_TABLE1 = [50, 50, 60, 30]  # جدول معلومات القسم
COL_WIDTHS_TABLE2 = [25, 20, 20, 20, 20, 30, 45, 10]  # جدول الأداءات الشهرية (معكوس)
COL_WIDTHS_TABLE3 = [60, 60, 70]  # جدول المجاميع

TABLE1_HEADERS = ['القيمة', 'البيان', 'القيمة', 'البيان']
TABLE2_HEADERS = ['تاريخ الدفع', 'حالة الدفع', 'المتبقي', 'المدفوع', 'المطلوب', 'رمز التلميذ', 'اسم التلميذ', 'الرقم']
TABLE3_HEADERS = ['النسبة المئوية', 'المبلغ', 'البيان']

ROW_HEIGHT_TABLE1 = 8
ROW_HEIGHT_TABLE2 = 7
ROW_HEIGHT_HEADER = 10

PAGE_MARGIN_TOP = 0.2
PAGE_MARGIN_BOTTOM = 0.2
PAGE_MARGIN_LEFT = 10
PAGE_MARGIN_RIGHT = 10

try:
    from fpdf import FPDF
    import arabic_reshaper
    from bidi.algorithm import get_display
except ImportError:
    subprocess.check_call([sys.executable, "-m", "pip", "install", "fpdf2", "arabic-reshaper", "python-bidi"])
    from fpdf import FPDF
    import arabic_reshaper
    from bidi.algorithm import get_display

class ArabicPDF(FPDF):
    def __init__(self):
        super().__init__('P','mm','A4')
        self.set_margins(PAGE_MARGIN_LEFT, PAGE_MARGIN_TOP, PAGE_MARGIN_RIGHT)
        self.set_auto_page_break(auto=True, margin=PAGE_MARGIN_BOTTOM)
        fonts_dir = os.path.join(os.path.dirname(__file__), 'fonts')
        
        # إضافة خطوط Calibri إذا كانت متوفرة
        calibri_path = os.path.join(fonts_dir, 'calibri.ttf')
        calibri_bold_path = os.path.join(fonts_dir, 'calibrib.ttf')
        
        if os.path.exists(calibri_path):
            self.add_font('Calibri', '', calibri_path)
            self.calibri_available = True
        else:
            self.calibri_available = False
            
        if os.path.exists(calibri_bold_path):
            self.add_font('Calibri', 'B', calibri_bold_path)
            self.calibri_bold_available = True
        else:
            self.calibri_bold_available = False
        
        # تعيين الخط الافتراضي
        if self.calibri_available:
            self.set_font('Calibri', '', 13)
        else:
            self.set_font('Arial', '', 13)
        self.set_line_width(0.4)

    def set_title_font(self, size=15):
        """تعيين خط العناوين"""
        if self.calibri_bold_available:
            self.set_font('Calibri', 'B', size)
        else:
            self.set_font('Arial', 'B', size)

    def set_detail_font(self, size=13):
        """تعيين خط التفاصيل"""
        if self.calibri_bold_available:
            self.set_font('Calibri', 'B', size)
        else:
            self.set_font('Arial', 'B', size)

    def set_normal_font(self, size=12):
        """تعيين الخط العادي"""
        if self.calibri_available:
            self.set_font('Calibri', '', size)
        else:
            self.set_font('Arial', '', size)

    def ar_text(self, txt: str) -> str:
        """تحويل النص العربي ليتم عرضه بشكل صحيح"""
        reshaped = arabic_reshaper.reshape(str(txt))
        return get_display(reshaped)

def get_section_info_from_db(db_path, section, month=None, year=None):
    """جلب معلومات القسم من جدول monthly_duties فقط"""
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # تحديد السنة إذا لم تكن محددة
        if year is None:
            year = datetime.now().year

        # جلب معلومات القسم من جدول monthly_duties فقط (الواجبات الشهرية فقط)
        cursor.execute("""
            SELECT DISTINCT
                اسم_الاستاذ,
                المادة,
                COALESCE(اسم_المجموعة, 'غير محدد') as المجموعة,
                COALESCE(نسبة_الاستاذ, 100) as نسبة_الواجبات
            FROM monthly_duties
            WHERE القسم = ?
            AND (النوع NOT LIKE '%تسجيل%' OR النوع IS NULL)
            AND amount_required > 0
            ORDER BY المادة
            LIMIT 1
        """, (section,))

        section_subjects = cursor.fetchall()

        # إذا لم توجد بيانات، استخدم قيم افتراضية
        if not section_subjects:
            section_subjects = [('غير محدد', 'غير محدد', 'غير محدد', 100)]

        # جلب إحصائيات التلاميذ من جدول monthly_duties فقط
        if month:
            print(f"🔍 جلب إحصائيات التلاميذ من جدول monthly_duties لـ {section} - {month}/{year}")

            # جلب الإحصائيات من جدول monthly_duties فقط باستخدام الرمز الفريد (الواجبات الشهرية فقط)
            cursor.execute("""
                SELECT COUNT(DISTINCT الرمز) as total_students,
                       COUNT(DISTINCT CASE WHEN الجنس = 'ذكر' THEN الرمز END) as male_count,
                       COUNT(DISTINCT CASE WHEN الجنس = 'أنثى' THEN الرمز END) as female_count
                FROM monthly_duties
                WHERE القسم = ? AND month = ? AND year = ? AND الرمز IS NOT NULL AND الرمز != ''
                AND (النوع NOT LIKE '%تسجيل%' OR النوع IS NULL)
                AND amount_required > 0
            """, (section, month, year))

            monthly_stats = cursor.fetchone()

            if monthly_stats and monthly_stats[0] > 0:
                print(f"✅ تم جلب الإحصائيات من جدول monthly_duties: {monthly_stats[0]} طالب")
                student_stats = monthly_stats
            else:
                print(f"⚠️ لا توجد إحصائيات في monthly_duties للشهر المحدد")
                # استخدام قيم افتراضية
                student_stats = (0, 0, 0)
        else:
            # إذا لم يتم تحديد شهر، جلب إحصائيات عامة من monthly_duties باستخدام الرمز الفريد (الواجبات الشهرية فقط)
            cursor.execute("""
                SELECT COUNT(DISTINCT الرمز) as total_students,
                       COUNT(DISTINCT CASE WHEN الجنس = 'ذكر' THEN الرمز END) as male_count,
                       COUNT(DISTINCT CASE WHEN الجنس = 'أنثى' THEN الرمز END) as female_count
                FROM monthly_duties
                WHERE القسم = ? AND الرمز IS NOT NULL AND الرمز != ''
                AND (النوع NOT LIKE '%تسجيل%' OR النوع IS NULL)
                AND amount_required > 0
            """, (section,))

            student_stats = cursor.fetchone()
            if not student_stats or student_stats[0] == 0:
                student_stats = (0, 0, 0)

        conn.close()

        return {
            'section_subjects': section_subjects,
            'student_stats': student_stats
        }

    except Exception as e:
        print(f"خطأ في جلب معلومات القسم: {str(e)}")
        return None

def get_monthly_duties_by_section_month(db_path, section, month, year=None):
    """جلب الأداءات الشهرية للقسم في الشهر المحدد من جدول monthly_duties فقط"""
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # تحديد السنة إذا لم تكن محددة
        if year is None:
            year = datetime.now().year

        print(f"🔍 جلب الأداءات الشهرية من جدول monthly_duties لـ {section} - {month}/{year}")

        # جلب البيانات مباشرة من جدول monthly_duties فقط (الواجبات الشهرية فقط، بدون واجبات التسجيل)
        cursor.execute("""
            SELECT
                اسم_التلميذ,
                الرمز,
                amount_required,
                amount_paid,
                amount_remaining,
                payment_status,
                payment_date,
                notes,
                COALESCE(اسم_الاستاذ, 'غير محدد') as اسم_الاستاذ
            FROM monthly_duties
            WHERE القسم = ? AND month = ? AND year = ?
            AND (النوع NOT LIKE '%تسجيل%' OR النوع IS NULL)
            AND amount_required > 0
            ORDER BY اسم_التلميذ
        """, (section, month, year))

        monthly_duties = cursor.fetchall()
        conn.close()

        print(f"✅ تم جلب {len(monthly_duties)} سجل من جدول monthly_duties")
        return monthly_duties

    except Exception as e:
        print(f"خطأ في جلب الأداءات الشهرية: {str(e)}")
        return []

def generate_section_monthly_report(logo_path, section_info, monthly_duties, section, month, output_path):
    """إنشاء تقرير القسم الشهري مقسم على صفحتين"""
    pdf = ArabicPDF()
    margin = 10
    usable_w = pdf.w - 2 * margin

    # ==================== الصفحة الأولى ====================
    pdf.add_page()
    y = pdf.get_y()
    
    # إضافة الشعار إذا كان متوفراً
    if logo_path and os.path.exists(logo_path):
        logo_w, logo_h = 60, 30
        x_logo = (pdf.w - logo_w) / 2
        pdf.image(logo_path, x=x_logo, y=y, w=logo_w, h=logo_h)
        y += logo_h + 5
        print(f"✅ تم إضافة الشعار إلى التقرير: {logo_path}")
    else:
        print(f"⚠️ لم يتم إضافة الشعار - الملف غير موجود أو المسار فارغ")
        y += 5  # مساحة صغيرة حتى لو لم يوجد شعار

    # عنوان التقرير
    pdf.set_draw_color(0, 51, 102)
    pdf.set_line_width(0.5)
    FIXED_BOX_HEIGHT = 12

    title_text = f"تقرير القسم الشهري - القسم: {section} - الشهر: {month}"
    
    pdf.set_text_color(0, 51, 102)
    pdf.set_xy(margin, y)
    pdf.set_title_font(15)
    pdf.cell(usable_w, FIXED_BOX_HEIGHT, pdf.ar_text(title_text), border=1, align='C')

    pdf.set_text_color(0, 0, 0)
    y += FIXED_BOX_HEIGHT + 10

    # ========== الجدول الأول: معلومات القسم والمواد ==========
    pdf.set_title_font(14)
    pdf.set_text_color(0, 100, 0)
    pdf.set_xy(margin, y)
    pdf.cell(usable_w, 8, pdf.ar_text('معلومات القسم والمواد'), border=0, align='C')
    pdf.set_text_color(0, 0, 0)
    y += 10

    cols1 = COL_WIDTHS_TABLE1
    
    # معلومات إحصائية عن القسم
    if section_info and section_info['student_stats']:
        stats = section_info['student_stats']

        # إضافة معلومات المواد
        if section_info['section_subjects']:
            first_subject = section_info['section_subjects'][0]
            teacher_name = first_subject[0] or 'غير محدد'
            subject_name = first_subject[1] or 'غير محدد'
            group_name = first_subject[2] or 'غير محدد'
            duties_percent = str(first_subject[3]) + '%' if first_subject[3] else '100%'

            # معلومات القسم
            section_info_rows = [
                [subject_name, 'المادة', str(stats[0]), 'إجمالي التلاميذ'],
                [teacher_name, 'الأستاذ(ة)', str(stats[1]), 'عدد الذكور'],
                [month, 'الشهر المحدد', str(stats[2]), 'عدد الإناث'],
                [duties_percent, 'حصة الأستاذ(ة) من المبلغ', group_name, 'المجموعة']
            ]
        else:
            # معلومات افتراضية
            section_info_rows = [
                ['غير محدد', 'المادة', str(stats[0]), 'إجمالي التلاميذ'],
                ['غير محدد', 'الأستاذ(ة)', str(stats[1]), 'عدد الذكور'],
                [month, 'الشهر المحدد', str(stats[2]), 'عدد الإناث'],
                ['100%', 'حصة الأستاذ(ة) من المبلغ', 'غير محدد', 'المجموعة']
            ]
    else:
        section_info_rows = [
            ['غير محدد', 'المادة', '0', 'إجمالي التلاميذ'],
            ['غير محدد', 'الأستاذ(ة)', '0', 'عدد الذكور'],
            [month, 'الشهر المحدد', '0', 'عدد الإناث'],
            ['100%', 'حصة الأستاذ(ة) من المبلغ', 'غير محدد', 'المجموعة']
        ]
    
    pdf.set_detail_font(13)
    pdf.set_fill_color(230, 240, 255)
    
    # رسم صفوف معلومات القسم
    for row in section_info_rows:
        x = margin
        for i, cell in enumerate(row):
            pdf.set_xy(x, y)
            fill = i % 2 == 1
            align = 'C' if i % 2 == 1 else 'R'
            pdf.cell(cols1[i], ROW_HEIGHT_TABLE1, pdf.ar_text(cell), border=1, align=align, fill=fill)
            x += cols1[i]
        y += ROW_HEIGHT_TABLE1
        
    y += 15

    # ========== الجدول الثالث: مجموع المبالغ وحصة الأستاذ ==========
    pdf.set_title_font(14)
    pdf.set_text_color(128, 0, 128)
    pdf.set_xy(margin, y)
    pdf.cell(usable_w, 8, pdf.ar_text('مجموع المبالغ وحصة الأستاذ(ة)'), border=0, align='C')
    pdf.set_text_color(0, 0, 0)
    y += 10

    cols3 = COL_WIDTHS_TABLE3
    
    # حساب المجاميع
    total_required = sum(float(duty[2]) for duty in monthly_duties if duty[2])
    total_paid = sum(float(duty[3]) for duty in monthly_duties if duty[3])
    total_remaining = sum(float(duty[4]) for duty in monthly_duties if duty[4])
    
    # الحصول على نسبة الأستاذ
    teacher_percentage = 100.0
    if section_info and section_info['section_subjects']:
        teacher_percentage = float(section_info['section_subjects'][0][3] or 100)
    
    # حساب حصة الأستاذ
    teacher_share = (total_paid * teacher_percentage) / 100
    institution_share = total_paid - teacher_share

    # إعداد بيانات جدول المجاميع
    totals_data = [
        [f"{total_required:.2f} درهم", f"{total_required:.2f} درهم", 'إجمالي المبلغ المطلوب'],
        [f"{total_paid:.2f} درهم", f"{total_paid:.2f} درهم", 'إجمالي المبلغ المدفوع'],
        [f"{total_remaining:.2f} درهم", f"{total_remaining:.2f} درهم", 'إجمالي المبلغ المتبقي'],
        [f"{teacher_percentage}%", f"{teacher_share:.2f} درهم", f'حصة الأستاذ(ة) ({teacher_percentage}%)'],
        [f"{100 - teacher_percentage}%", f"{institution_share:.2f} درهم", f'حصة المؤسسة ({100 - teacher_percentage}%)']
    ]

    # رسم رأس الجدول الثالث
    pdf.set_detail_font(13)
    pdf.set_fill_color(255, 200, 255)
    
    x = margin
    for i, header in enumerate(TABLE3_HEADERS):
        pdf.set_xy(x, y)
        pdf.cell(cols3[i], ROW_HEIGHT_HEADER, pdf.ar_text(header), border=1, align='C', fill=True)
        x += cols3[i]

    y += ROW_HEIGHT_HEADER

    # رسم بيانات المجاميع
    pdf.set_normal_font(12)
    
    for i, row in enumerate(totals_data):
        x = margin
        # تلوين متناوب
        if i % 2 == 0:
            pdf.set_fill_color(255, 248, 255)
        else:
            pdf.set_fill_color(248, 240, 255)
            
        for j, cell in enumerate(row):
            pdf.set_xy(x, y)
            pdf.cell(cols3[j], ROW_HEIGHT_TABLE1, pdf.ar_text(cell), border=1, align='C', fill=True)
            x += cols3[j]
        y += ROW_HEIGHT_TABLE1

    # ========== التوقيعات ==========
    y += 20
    pdf.set_title_font(14)
    pdf.set_text_color(0, 51, 102)
    pdf.set_xy(margin, y)
    pdf.cell(usable_w, 8, pdf.ar_text('التوقيعات'), border=0, align='C')
    pdf.set_text_color(0, 0, 0)
    y += 20

    pdf.set_normal_font(12)

    # توقيع المسؤول
    pdf.set_xy(margin, y)
    pdf.cell(usable_w/2 - 10, 8, pdf.ar_text('توقيع المسؤول'), border=0, align='C')

    # توقيع الأستاذ(ة)
    pdf.set_xy(margin + usable_w/2 + 10, y)
    pdf.cell(usable_w/2 - 10, 8, pdf.ar_text('توقيع الأستاذ(ة)'), border=0, align='C')

    # خطوط التوقيع
    y += 15
    pdf.set_xy(margin, y)
    pdf.cell(usable_w/2 - 10, 8, pdf.ar_text('_' * 20), border=0, align='C')

    pdf.set_xy(margin + usable_w/2 + 10, y)
    pdf.cell(usable_w/2 - 10, 8, pdf.ar_text('_' * 20), border=0, align='C')

    # ==================== الصفحة الثانية ====================
    pdf.add_page()
    y = pdf.get_y()

    # إضافة الشعار في الصفحة الثانية
    if logo_path and os.path.exists(logo_path):
        logo_w, logo_h = 60, 30
        x_logo = (pdf.w - logo_w) / 2
        pdf.image(logo_path, x=x_logo, y=y, w=logo_w, h=logo_h)
        y += logo_h + 5
        print(f"✅ تم إضافة الشعار إلى الصفحة الثانية")
    else:
        y += 2  # مساحة صغيرة حتى لو لم يوجد شعار

    # عنوان الصفحة الثانية
    pdf.set_draw_color(0, 51, 102)
    pdf.set_line_width(0.5)

    title_text = f"تفاصيل الأداءات الشهرية - القسم: {section} - الشهر: {month}"
    
    pdf.set_text_color(0, 51, 102)
    pdf.set_xy(margin, y)
    pdf.set_title_font(15)
    pdf.cell(usable_w, FIXED_BOX_HEIGHT, pdf.ar_text(title_text), border=1, align='C')

    pdf.set_text_color(0, 0, 0)
    y += FIXED_BOX_HEIGHT + 2

    # ========== الجدول الثاني: جدول الأداءات الشهرية ==========
    pdf.set_title_font(14)
    pdf.set_text_color(0, 100, 0)
    pdf.set_xy(margin, y)
    pdf.cell(usable_w, 5, pdf.ar_text('جدول الأداءات الشهرية'), border=0, align='C')
    pdf.set_text_color(0, 0, 0)
    y += 10

    cols2 = COL_WIDTHS_TABLE2
    
    # رسم رأس الجدول الثاني
    pdf.set_detail_font(13)
    pdf.set_fill_color(200, 255, 200)
    
    x = margin
    for i, header in enumerate(TABLE2_HEADERS):
        pdf.set_xy(x, y)
        pdf.cell(cols2[i], ROW_HEIGHT_HEADER, pdf.ar_text(header), border=1, align='C', fill=True)
        x += cols2[i]

    y += ROW_HEIGHT_HEADER

    # رسم بيانات الأداءات الشهرية
    pdf.set_normal_font(11)
    
    if monthly_duties:
        for i, duty in enumerate(monthly_duties):
            x = margin
            # ترتيب البيانات الصحيح حسب الاستعلام
            # duty = (اسم_التلميذ, الرمز, amount_required, amount_paid, amount_remaining, payment_status, payment_date, notes, اسم_الاستاذ)
            student_name = duty[0] if duty[0] else 'غير محدد'
            student_code = duty[1] if duty[1] else str(i + 1)
            amount_required = f'{float(duty[2]):.2f} درهم' if duty[2] else '0.00 درهم'
            amount_paid = f'{float(duty[3]):.2f} درهم' if duty[3] else '0.00 درهم'
            amount_remaining = f'{float(duty[4]):.2f} درهم' if duty[4] else '0.00 درهم'
            payment_status = duty[5] if duty[5] else 'غير محدد'
            payment_date = duty[6] if duty[6] else 'غير محدد'

            data = [
                payment_date,      # تاريخ الدفع
                payment_status,    # حالة الدفع
                amount_remaining,  # المتبقي
                amount_paid,       # المدفوع
                amount_required,   # المطلوب
                student_code,      # رمز التلميذ
                student_name,      # اسم التلميذ
                str(i + 1)         # الرقم
            ]

            # تلوين حسب حالة الدفع
            if payment_status == "مدفوع كاملاً":
                pdf.set_fill_color(220, 255, 220)  # أخضر فاتح
            elif payment_status == "مدفوع جزئياً":
                pdf.set_fill_color(255, 255, 200)  # أصفر فاتح
            else:
                pdf.set_fill_color(255, 220, 220)  # أحمر فاتح

            for j, cell in enumerate(data):
                pdf.set_xy(x, y)
                pdf.cell(cols2[j], ROW_HEIGHT_TABLE2, pdf.ar_text(cell), border=1, align='C', fill=True)
                x += cols2[j]

            y += ROW_HEIGHT_TABLE2

            # الانتقال إلى صفحة جديدة عند الحاجة
            if y > pdf.h - 50:
                pdf.add_page()
                y = pdf.get_y()

                # إضافة الشعار في الصفحة الجديدة
                if logo_path and os.path.exists(logo_path):
                    logo_w, logo_h = 60, 30
                    x_logo = (pdf.w - logo_w) / 2
                    pdf.image(logo_path, x=x_logo, y=y, w=logo_w, h=logo_h)
                    y += logo_h + 5
                    print(f"✅ تم إضافة الشعار إلى الصفحة الجديدة")
                else:
                    y += 5  # مساحة صغيرة حتى لو لم يوجد شعار

                # إضافة عنوان التقرير في الصفحة الجديدة
                pdf.set_draw_color(0, 51, 102)
                pdf.set_line_width(0.5)

                title_text = f"تفاصيل الأداءات الشهرية - القسم: {section} - الشهر: {month} (تابع)"

                pdf.set_text_color(0, 51, 102)
                pdf.set_xy(margin, y)
                pdf.set_title_font(15)
                pdf.cell(usable_w, FIXED_BOX_HEIGHT, pdf.ar_text(title_text), border=1, align='C')

                pdf.set_text_color(0, 0, 0)
                y += FIXED_BOX_HEIGHT + 5                # إعادة رسم رأس الجدول
                pdf.set_detail_font(13)
                pdf.set_fill_color(200, 255, 200)
                
                x = margin
                for k, header in enumerate(TABLE2_HEADERS):
                    pdf.set_xy(x, y)
                    pdf.cell(cols2[k], ROW_HEIGHT_HEADER, pdf.ar_text(header), border=1, align='C', fill=True)
                    x += cols2[k]
                
                y += ROW_HEIGHT_HEADER
                pdf.set_normal_font(11)
    else:
        # إذا لم توجد أداءات شهرية
        pdf.set_xy(margin, y)
        pdf.set_fill_color(255, 245, 245)
        pdf.cell(sum(cols2), ROW_HEIGHT_TABLE2, pdf.ar_text('لا توجد أداءات شهرية مسجلة'), border=1, align='C', fill=True)

    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    pdf.output(output_path)
    print(f"✅ تم إنشاء تقرير القسم الشهري مقسم على صفحتين: {output_path}")
    if logo_path:
        print(f"🖼️ تم تضمين الشعار من: {logo_path}")
    else:
        print(f"⚠️ لم يتم تضمين الشعار - الملف غير موجود")

def print_section_monthly_report(parent=None, section=None, month=None):
    """
    دالة لإنشاء تقرير القسم الشهري
    
    المعاملات:
        parent: كائن النافذة الأم
        section: اسم القسم
        month: الشهر

    العوائد:
        (success, output_path, message): ثلاثية تحدد نجاح العملية ومسار الملف ورسالة النتيجة
    """
    try:
        if not section:
            return False, None, "اسم القسم غير محدد."
        
        if not month:
            return False, None, "الشهر غير محدد."

        # تحديد مسار قاعدة البيانات
        db_path = get_database_path()
        
        # جلب بيانات القسم والأداءات الشهرية
        try:            # جلب شعار المؤسسة من قاعدة البيانات أو البحث في المجلدات
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            cursor.execute("SELECT ImagePath1 FROM بيانات_المؤسسة LIMIT 1")
            logo_row = cursor.fetchone()
            logo_path = logo_row[0] if logo_row and logo_row[0] and os.path.exists(logo_row[0]) else None

            # إذا لم يوجد الشعار في قاعدة البيانات، ابحث في المجلدات المحتملة
            if not logo_path:
                possible_logo_paths = [
                    os.path.join(os.path.dirname(__file__), 'logo.png'),
                    os.path.join(os.path.dirname(__file__), 'logo.jpg'),
                    os.path.join(os.path.dirname(__file__), 'logo.jpeg'),
                    os.path.join(os.path.expanduser('~'), 'Desktop', 'logo.png'),
                    os.path.join(os.path.expanduser('~'), 'Desktop', 'logo.jpg'),
                    os.path.join(os.path.expanduser('~'), 'Desktop', 'logo.jpeg')
                ]

                for path in possible_logo_paths:
                    if os.path.exists(path):
                        logo_path = path
                        print(f"🖼️ تم العثور على الشعار في: {logo_path}")
                        break

            if logo_path:
                print(f"✅ سيتم استخدام الشعار: {logo_path}")
            else:
                print(f"⚠️ لم يتم العثور على ملف الشعار")
            
            conn.close()

            # تحديد السنة الحالية
            current_year = datetime.now().year

            # جلب معلومات القسم مع الإحصائيات من البيانات المرحلة
            section_info = get_section_info_from_db(db_path, section, month, current_year)

            # جلب الأداءات الشهرية من البيانات المرحلة
            monthly_duties = get_monthly_duties_by_section_month(db_path, section, month, current_year)
            
        except Exception as db_error:
            print(f"خطأ في الوصول لقاعدة البيانات: {db_error}")
            return False, None, f"خطأ في الوصول لقاعدة البيانات: {str(db_error)}"

        # إنشاء مجلد التقارير إذا لم يكن موجوداً
        reports_dir = os.path.join(os.path.expanduser('~'), 'Desktop', 'تقارير الأقسام الشهرية')
        if not os.path.exists(reports_dir):
            os.makedirs(reports_dir)

        # تحديد اسم الملف
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        section_clean = section.replace(' ', '_').replace('/', '_')
        output_path = os.path.join(reports_dir, f"تقرير_القسم_{section_clean}_{month}_{timestamp}.pdf")

        # إنشاء التقرير
        generate_section_monthly_report(logo_path, section_info, monthly_duties, section, month, output_path)

        # فتح الملف بعد إنشائه
        try:
            if sys.platform == 'win32':
                os.startfile(output_path)
            elif sys.platform == 'darwin':  # macOS
                subprocess.call(['open', output_path])
            else:  # Linux
                subprocess.call(['xdg-open', output_path])
        except Exception as e:
            return True, output_path, f"تم إنشاء التقرير ولكن تعذر فتح الملف: {str(e)}"

        return True, output_path, "تم إنشاء تقرير القسم الشهري بنجاح."
        
    except Exception as e:
        traceback.print_exc()
        return False, None, f"حدث خطأ في إنشاء تقرير القسم الشهري: {str(e)}"

if __name__=='__main__':
    # مثال على الاستخدام
    try:
        success, output_path, message = print_section_monthly_report(
            section="قسم / 01", 
            month="يناير"
        )
        
        print(f"النتيجة: {success}")
        print(f"المسار: {output_path}")
        print(f"الرسالة: {message}")
        
    except Exception as e:
        print(f"خطأ في الاختبار: {e}")
        traceback.print_exc()
