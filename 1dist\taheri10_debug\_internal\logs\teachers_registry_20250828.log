2025-08-28 19:01:08 - TeachersRegistryWindow - INFO - بدء تشغيل نافذة سجلات الأساتذة
2025-08-28 19:01:08 - TeachersRegistryWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-08-28 19:01:08 - TeachersRegistryWindow - INFO - تم تحميل النافذة بنجاح
2025-08-28 19:01:17 - TeachersRegistryWindow - INFO - العملية: تحديث نسبة الأستاذ - التفاصيل: القسم: قسم / 04, الشهر: يونيو, النسبة: 70%
2025-08-28 19:01:28 - TeachersRegistryWindow - INFO - العملية: إغلاق النافذة
2025-08-28 19:13:51 - TeachersRegistryWindow - INFO - بدء تشغيل نافذة سجلات الأساتذة
2025-08-28 19:13:51 - <PERSON><PERSON><PERSON><PERSON>ryWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-08-28 19:13:51 - TeachersRegistryWindow - INFO - تم تحميل النافذة بنجاح
2025-08-28 19:14:05 - TeachersRegistryWindow - INFO - العملية: تحديث نسبة الأستاذ - التفاصيل: القسم: قسم / 04, الشهر: يونيو, النسبة: 70%
2025-08-28 19:14:27 - TeachersRegistryWindow - INFO - العملية: إغلاق النافذة
2025-08-28 19:30:15 - TeachersRegistryWindow - INFO - بدء تشغيل نافذة سجلات الأساتذة
2025-08-28 19:30:15 - TeachersRegistryWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-08-28 19:30:15 - TeachersRegistryWindow - INFO - تم تحميل النافذة بنجاح
2025-08-28 19:30:27 - TeachersRegistryWindow - INFO - العملية: إغلاق النافذة
2025-08-28 19:31:08 - TeachersRegistryWindow - INFO - بدء تشغيل نافذة سجلات الأساتذة
2025-08-28 19:31:08 - TeachersRegistryWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-08-28 19:31:08 - TeachersRegistryWindow - INFO - تم تحميل النافذة بنجاح
2025-08-28 19:31:20 - TeachersRegistryWindow - INFO - العملية: إغلاق النافذة
2025-08-28 19:31:53 - TeachersRegistryWindow - INFO - بدء تشغيل نافذة سجلات الأساتذة
2025-08-28 19:31:53 - TeachersRegistryWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-08-28 19:31:53 - TeachersRegistryWindow - INFO - تم تحميل النافذة بنجاح
2025-08-28 19:32:03 - TeachersRegistryWindow - INFO - العملية: إغلاق النافذة
2025-08-28 19:32:28 - TeachersRegistryWindow - INFO - بدء تشغيل نافذة سجلات الأساتذة
2025-08-28 19:32:28 - TeachersRegistryWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-08-28 19:32:28 - TeachersRegistryWindow - INFO - تم تحميل النافذة بنجاح
2025-08-28 19:32:41 - TeachersRegistryWindow - INFO - العملية: إغلاق النافذة
2025-08-28 19:33:12 - TeachersRegistryWindow - INFO - بدء تشغيل نافذة سجلات الأساتذة
2025-08-28 19:33:12 - TeachersRegistryWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-08-28 19:33:12 - TeachersRegistryWindow - INFO - تم تحميل النافذة بنجاح
2025-08-28 19:33:22 - TeachersRegistryWindow - INFO - العملية: إغلاق النافذة
2025-08-28 19:33:52 - TeachersRegistryWindow - INFO - بدء تشغيل نافذة سجلات الأساتذة
2025-08-28 19:33:52 - TeachersRegistryWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-08-28 19:33:52 - TeachersRegistryWindow - INFO - تم تحميل النافذة بنجاح
2025-08-28 19:34:07 - TeachersRegistryWindow - INFO - العملية: إغلاق النافذة
2025-08-28 19:34:41 - TeachersRegistryWindow - INFO - بدء تشغيل نافذة سجلات الأساتذة
2025-08-28 19:34:41 - TeachersRegistryWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-08-28 19:34:41 - TeachersRegistryWindow - INFO - تم تحميل النافذة بنجاح
2025-08-28 19:34:51 - TeachersRegistryWindow - INFO - العملية: إغلاق النافذة
2025-08-28 19:35:19 - TeachersRegistryWindow - INFO - بدء تشغيل نافذة سجلات الأساتذة
2025-08-28 19:35:20 - TeachersRegistryWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-08-28 19:35:20 - TeachersRegistryWindow - INFO - تم تحميل النافذة بنجاح
2025-08-28 19:35:29 - TeachersRegistryWindow - INFO - العملية: إغلاق النافذة
2025-08-28 19:36:06 - TeachersRegistryWindow - INFO - بدء تشغيل نافذة سجلات الأساتذة
2025-08-28 19:36:06 - TeachersRegistryWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-08-28 19:36:06 - TeachersRegistryWindow - INFO - تم تحميل النافذة بنجاح
2025-08-28 19:36:16 - TeachersRegistryWindow - INFO - العملية: إغلاق النافذة
2025-08-28 19:36:46 - TeachersRegistryWindow - INFO - بدء تشغيل نافذة سجلات الأساتذة
2025-08-28 19:36:46 - TeachersRegistryWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-08-28 19:36:46 - TeachersRegistryWindow - INFO - تم تحميل النافذة بنجاح
2025-08-28 19:36:57 - TeachersRegistryWindow - INFO - العملية: إغلاق النافذة
2025-08-28 19:37:31 - TeachersRegistryWindow - INFO - بدء تشغيل نافذة سجلات الأساتذة
2025-08-28 19:37:31 - TeachersRegistryWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-08-28 19:37:31 - TeachersRegistryWindow - INFO - تم تحميل النافذة بنجاح
2025-08-28 19:37:45 - TeachersRegistryWindow - ERROR - خطأ في طباعة سجلات الأساتذة: name 'base_events' is not defined
2025-08-28 19:37:45 - TeachersRegistryWindow - DEBUG - تفاصيل الخطأ:
Traceback (most recent call last):
  File "print111.py", line 61, in <module>
    from fpdf import FPDF
  File "PyInstaller\loader\pyimod02_importers.py", line 457, in exec_module
  File "fpdf\__init__.py", line 24, in <module>
  File "PyInstaller\loader\pyimod02_importers.py", line 457, in exec_module
  File "fpdf\fpdf.py", line 109, in <module>
  File "PyInstaller\loader\pyimod02_importers.py", line 457, in exec_module
  File "fpdf\image_parsing.py", line 25, in <module>
  File "PyInstaller\loader\pyimod02_importers.py", line 457, in exec_module
  File "fpdf\svg.py", line 38, in <module>
  File "PyInstaller\loader\pyimod02_importers.py", line 457, in exec_module
  File "fpdf\output.py", line 25, in <module>
  File "PyInstaller\loader\pyimod02_importers.py", line 457, in exec_module
  File "fpdf\sign.py", line 11, in <module>
  File "PyInstaller\loader\pyimod02_importers.py", line 457, in exec_module
  File "unittest\mock.py", line 27, in <module>
  File "PyInstaller\loader\pyimod02_importers.py", line 457, in exec_module
  File "asyncio\__init__.py", line 43, in <module>
  File "PyInstaller\loader\pyimod02_importers.py", line 457, in exec_module
  File "asyncio\windows_events.py", line 8, in <module>
ModuleNotFoundError: No module named '_overlapped'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "sub26662_window.py", line 861, in print_teachers_registry
    import print111
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "PyInstaller\loader\pyimod02_importers.py", line 457, in exec_module
  File "print111.py", line 66, in <module>
    from fpdf import FPDF
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "PyInstaller\loader\pyimod02_importers.py", line 457, in exec_module
  File "fpdf\__init__.py", line 24, in <module>
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "PyInstaller\loader\pyimod02_importers.py", line 457, in exec_module
  File "fpdf\fpdf.py", line 109, in <module>
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "PyInstaller\loader\pyimod02_importers.py", line 457, in exec_module
  File "fpdf\image_parsing.py", line 25, in <module>
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "PyInstaller\loader\pyimod02_importers.py", line 457, in exec_module
  File "fpdf\svg.py", line 38, in <module>
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "PyInstaller\loader\pyimod02_importers.py", line 457, in exec_module
  File "fpdf\output.py", line 25, in <module>
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "PyInstaller\loader\pyimod02_importers.py", line 457, in exec_module
  File "fpdf\sign.py", line 11, in <module>
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "PyInstaller\loader\pyimod02_importers.py", line 457, in exec_module
  File "unittest\mock.py", line 27, in <module>
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "PyInstaller\loader\pyimod02_importers.py", line 457, in exec_module
  File "asyncio\__init__.py", line 25, in <module>
NameError: name 'base_events' is not defined

2025-08-28 19:37:48 - TeachersRegistryWindow - INFO - العملية: إغلاق النافذة
2025-08-28 19:38:30 - TeachersRegistryWindow - INFO - بدء تشغيل نافذة سجلات الأساتذة
2025-08-28 19:38:30 - TeachersRegistryWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-08-28 19:38:30 - TeachersRegistryWindow - INFO - تم تحميل النافذة بنجاح
2025-08-28 19:38:41 - TeachersRegistryWindow - INFO - العملية: إغلاق النافذة
2025-08-28 19:38:53 - TeachersRegistryWindow - INFO - بدء تشغيل نافذة سجلات الأساتذة
2025-08-28 19:38:53 - TeachersRegistryWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-08-28 19:38:53 - TeachersRegistryWindow - INFO - تم تحميل النافذة بنجاح
2025-08-28 19:39:01 - TeachersRegistryWindow - ERROR - خطأ في طباعة سجلات الأساتذة: name 'base_events' is not defined
2025-08-28 19:39:01 - TeachersRegistryWindow - DEBUG - تفاصيل الخطأ:
Traceback (most recent call last):
  File "print111.py", line 61, in <module>
    from fpdf import FPDF
  File "PyInstaller\loader\pyimod02_importers.py", line 457, in exec_module
  File "fpdf\__init__.py", line 24, in <module>
  File "PyInstaller\loader\pyimod02_importers.py", line 457, in exec_module
  File "fpdf\fpdf.py", line 109, in <module>
  File "PyInstaller\loader\pyimod02_importers.py", line 457, in exec_module
  File "fpdf\image_parsing.py", line 25, in <module>
  File "PyInstaller\loader\pyimod02_importers.py", line 457, in exec_module
  File "fpdf\svg.py", line 38, in <module>
  File "PyInstaller\loader\pyimod02_importers.py", line 457, in exec_module
  File "fpdf\output.py", line 25, in <module>
  File "PyInstaller\loader\pyimod02_importers.py", line 457, in exec_module
  File "fpdf\sign.py", line 11, in <module>
  File "PyInstaller\loader\pyimod02_importers.py", line 457, in exec_module
  File "unittest\mock.py", line 27, in <module>
  File "PyInstaller\loader\pyimod02_importers.py", line 457, in exec_module
  File "asyncio\__init__.py", line 43, in <module>
  File "PyInstaller\loader\pyimod02_importers.py", line 457, in exec_module
  File "asyncio\windows_events.py", line 8, in <module>
ModuleNotFoundError: No module named '_overlapped'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "sub26662_window.py", line 861, in print_teachers_registry
    import print111
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "PyInstaller\loader\pyimod02_importers.py", line 457, in exec_module
  File "print111.py", line 66, in <module>
    from fpdf import FPDF
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "PyInstaller\loader\pyimod02_importers.py", line 457, in exec_module
  File "fpdf\__init__.py", line 24, in <module>
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "PyInstaller\loader\pyimod02_importers.py", line 457, in exec_module
  File "fpdf\fpdf.py", line 109, in <module>
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "PyInstaller\loader\pyimod02_importers.py", line 457, in exec_module
  File "fpdf\image_parsing.py", line 25, in <module>
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "PyInstaller\loader\pyimod02_importers.py", line 457, in exec_module
  File "fpdf\svg.py", line 38, in <module>
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "PyInstaller\loader\pyimod02_importers.py", line 457, in exec_module
  File "fpdf\output.py", line 25, in <module>
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "PyInstaller\loader\pyimod02_importers.py", line 457, in exec_module
  File "fpdf\sign.py", line 11, in <module>
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "PyInstaller\loader\pyimod02_importers.py", line 457, in exec_module
  File "unittest\mock.py", line 27, in <module>
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "PyInstaller\loader\pyimod02_importers.py", line 457, in exec_module
  File "asyncio\__init__.py", line 25, in <module>
NameError: name 'base_events' is not defined

2025-08-28 19:39:09 - TeachersRegistryWindow - INFO - العملية: إغلاق النافذة
2025-08-28 19:39:38 - TeachersRegistryWindow - INFO - بدء تشغيل نافذة سجلات الأساتذة
2025-08-28 19:39:38 - TeachersRegistryWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-08-28 19:39:38 - TeachersRegistryWindow - INFO - تم تحميل النافذة بنجاح
2025-08-28 19:39:48 - TeachersRegistryWindow - INFO - العملية: إغلاق النافذة
2025-08-28 19:40:16 - TeachersRegistryWindow - INFO - بدء تشغيل نافذة سجلات الأساتذة
2025-08-28 19:40:16 - TeachersRegistryWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-08-28 19:40:16 - TeachersRegistryWindow - INFO - تم تحميل النافذة بنجاح
2025-08-28 19:40:26 - TeachersRegistryWindow - INFO - العملية: إغلاق النافذة
2025-08-28 19:41:09 - TeachersRegistryWindow - INFO - بدء تشغيل نافذة سجلات الأساتذة
2025-08-28 19:41:09 - TeachersRegistryWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-08-28 19:41:09 - TeachersRegistryWindow - INFO - تم تحميل النافذة بنجاح
2025-08-28 19:41:21 - TeachersRegistryWindow - INFO - العملية: إغلاق النافذة
2025-08-28 19:41:53 - TeachersRegistryWindow - INFO - بدء تشغيل نافذة سجلات الأساتذة
2025-08-28 19:41:53 - TeachersRegistryWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-08-28 19:41:53 - TeachersRegistryWindow - INFO - تم تحميل النافذة بنجاح
2025-08-28 19:42:02 - TeachersRegistryWindow - INFO - العملية: إغلاق النافذة
2025-08-28 19:42:16 - TeachersRegistryWindow - INFO - بدء تشغيل نافذة سجلات الأساتذة
2025-08-28 19:42:16 - TeachersRegistryWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-08-28 19:42:16 - TeachersRegistryWindow - INFO - تم تحميل النافذة بنجاح
2025-08-28 19:42:27 - TeachersRegistryWindow - ERROR - خطأ في طباعة سجلات الأساتذة: name 'base_events' is not defined
2025-08-28 19:42:27 - TeachersRegistryWindow - DEBUG - تفاصيل الخطأ:
Traceback (most recent call last):
  File "print111.py", line 61, in <module>
    from fpdf import FPDF
  File "PyInstaller\loader\pyimod02_importers.py", line 457, in exec_module
  File "fpdf\__init__.py", line 24, in <module>
  File "PyInstaller\loader\pyimod02_importers.py", line 457, in exec_module
  File "fpdf\fpdf.py", line 109, in <module>
  File "PyInstaller\loader\pyimod02_importers.py", line 457, in exec_module
  File "fpdf\image_parsing.py", line 25, in <module>
  File "PyInstaller\loader\pyimod02_importers.py", line 457, in exec_module
  File "fpdf\svg.py", line 38, in <module>
  File "PyInstaller\loader\pyimod02_importers.py", line 457, in exec_module
  File "fpdf\output.py", line 25, in <module>
  File "PyInstaller\loader\pyimod02_importers.py", line 457, in exec_module
  File "fpdf\sign.py", line 11, in <module>
  File "PyInstaller\loader\pyimod02_importers.py", line 457, in exec_module
  File "unittest\mock.py", line 27, in <module>
  File "PyInstaller\loader\pyimod02_importers.py", line 457, in exec_module
  File "asyncio\__init__.py", line 43, in <module>
  File "PyInstaller\loader\pyimod02_importers.py", line 457, in exec_module
  File "asyncio\windows_events.py", line 8, in <module>
ModuleNotFoundError: No module named '_overlapped'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "sub26662_window.py", line 861, in print_teachers_registry
    import print111
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "PyInstaller\loader\pyimod02_importers.py", line 457, in exec_module
  File "print111.py", line 66, in <module>
    from fpdf import FPDF
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "PyInstaller\loader\pyimod02_importers.py", line 457, in exec_module
  File "fpdf\__init__.py", line 24, in <module>
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "PyInstaller\loader\pyimod02_importers.py", line 457, in exec_module
  File "fpdf\fpdf.py", line 109, in <module>
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "PyInstaller\loader\pyimod02_importers.py", line 457, in exec_module
  File "fpdf\image_parsing.py", line 25, in <module>
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "PyInstaller\loader\pyimod02_importers.py", line 457, in exec_module
  File "fpdf\svg.py", line 38, in <module>
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "PyInstaller\loader\pyimod02_importers.py", line 457, in exec_module
  File "fpdf\output.py", line 25, in <module>
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "PyInstaller\loader\pyimod02_importers.py", line 457, in exec_module
  File "fpdf\sign.py", line 11, in <module>
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "PyInstaller\loader\pyimod02_importers.py", line 457, in exec_module
  File "unittest\mock.py", line 27, in <module>
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "PyInstaller\loader\pyimod02_importers.py", line 457, in exec_module
  File "asyncio\__init__.py", line 25, in <module>
NameError: name 'base_events' is not defined

2025-08-28 19:42:30 - TeachersRegistryWindow - INFO - العملية: إغلاق النافذة
2025-08-28 19:43:15 - TeachersRegistryWindow - INFO - بدء تشغيل نافذة سجلات الأساتذة
2025-08-28 19:43:15 - TeachersRegistryWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-08-28 19:43:15 - TeachersRegistryWindow - INFO - تم تحميل النافذة بنجاح
2025-08-28 19:43:26 - TeachersRegistryWindow - INFO - العملية: إغلاق النافذة
2025-08-28 19:44:06 - TeachersRegistryWindow - INFO - بدء تشغيل نافذة سجلات الأساتذة
2025-08-28 19:44:06 - TeachersRegistryWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-08-28 19:44:06 - TeachersRegistryWindow - INFO - تم تحميل النافذة بنجاح
2025-08-28 19:44:17 - TeachersRegistryWindow - INFO - العملية: إغلاق النافذة
2025-08-28 19:44:53 - TeachersRegistryWindow - INFO - بدء تشغيل نافذة سجلات الأساتذة
2025-08-28 19:44:53 - TeachersRegistryWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-08-28 19:44:53 - TeachersRegistryWindow - INFO - تم تحميل النافذة بنجاح
2025-08-28 19:45:04 - TeachersRegistryWindow - INFO - العملية: إغلاق النافذة
2025-08-28 19:45:35 - TeachersRegistryWindow - INFO - بدء تشغيل نافذة سجلات الأساتذة
2025-08-28 19:45:35 - TeachersRegistryWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-08-28 19:45:35 - TeachersRegistryWindow - INFO - تم تحميل النافذة بنجاح
2025-08-28 19:45:46 - TeachersRegistryWindow - INFO - العملية: إغلاق النافذة
2025-08-28 19:47:03 - TeachersRegistryWindow - INFO - بدء تشغيل نافذة سجلات الأساتذة
2025-08-28 19:47:03 - TeachersRegistryWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-08-28 19:47:03 - TeachersRegistryWindow - INFO - تم تحميل النافذة بنجاح
2025-08-28 19:47:22 - TeachersRegistryWindow - INFO - العملية: إغلاق النافذة
2025-08-28 19:47:37 - TeachersRegistryWindow - INFO - بدء تشغيل نافذة سجلات الأساتذة
2025-08-28 19:47:37 - TeachersRegistryWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-08-28 19:47:37 - TeachersRegistryWindow - INFO - تم تحميل النافذة بنجاح
2025-08-28 19:48:08 - TeachersRegistryWindow - INFO - العملية: إغلاق النافذة
