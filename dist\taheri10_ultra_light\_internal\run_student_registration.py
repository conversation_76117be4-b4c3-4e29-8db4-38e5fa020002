
#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
ملف تشغيل نظام تسجيل التلاميذ في أقسام متعددة
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMessageBox
from PyQt5.QtCore import Qt
from student_multi_registration import StudentMultiSectionRegistrationWindow

def main():
    """الدالة الرئيسية لتشغيل النظام"""
    
    # إنشاء تطبيق PyQt5
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    # تعيين خصائص التطبيق
    app.setApplicationName("نظام تسجيل التلاميذ في أقسام متعددة")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("نظام إدارة المدرسة")
    
    try:
        # إنشاء النافذة الرئيسية
        window = StudentMultiSectionRegistrationWindow()
        
        # عرض النافذة
        window.show()
        
        # تشغيل التطبيق
        sys.exit(app.exec_())
        
    except Exception as e:
        # في حالة حدوث خطأ
        QMessageBox.critical(None, "خطأ في التشغيل", 
                           f"حدث خطأ أثناء تشغيل النظام:\n{str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
