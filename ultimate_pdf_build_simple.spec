# -*- mode: python ; coding: utf-8 -*-
"""
ملف تحزيم مبسط وآمن - إصدار خفيف جداً
=====================================

آخر تحديث: 2025-08-28
هذا الإصدار مبسط لتجنب مشاكل التحزيم والحصول على أصغر حجم ممكن
"""

import os
block_cipher = None

# ملفات البيانات الأساسية فقط
datas=[
    # الأيقونة
    ('01.ico', '.'),
    
    # قاعدة البيانات
    ('data.db', '.'),
    
    # الخطوط الأساسية فقط
    ('fonts/Arial.ttf', 'fonts/'),
    ('fonts/arialbd.ttf', 'fonts/'),
    ('fonts/calibri.ttf', 'fonts/'),
    ('fonts/calibrib.ttf', 'fonts/'),
]

a = Analysis(
    ['main_window.py'],
    pathex=[r'c:\Users\<USER>\Desktop\csv\taheri10'],
    binaries=[],
    datas=datas,
    hiddenimports=[
        # النوافذ الأساسية
        'main_window',
        'sub01_window',
        'sub2_window',
        'sub8_window',
        'sub252_window',
        'sub262_window',
        'sub263_window',
        'sub26662_window',
        'sub3_window',
        'sub4_window',
        'sub100_window',
        'expense_management_window',
        'cash_flow_window',
        'financial_system_launcher',
        'monthly_duties_window',
        'attendance_processing_window',
        'student_multi_registration',
        'run_student_registration',
        'check_db_structure',
        
        # ملفات الطباعة
        'print101',
        'print111',
        'print144',
        'print_registration_fees',
        'print_registration_fees_monthly_style',
        'print_registration_fees_simple',
        'print_registration_fees_all_sections',
        'print1_section_monthly',
        'print_section_monthly',
        'print_section_yearly',
        'attendance_sheet_report',
        'daily_attendance_sheet_report',
        
        # المكتبات الأساسية فقط
        'fpdf',
        'arabic_reshaper',
        'bidi',
        'bidi.algorithm',
        'PyQt5.QtWidgets',
        'PyQt5.QtGui',
        'PyQt5.QtCore',
        'PyQt5.sip',
        'sqlite3',
        '_sqlite3',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        # استبعاد المكتبات الثقيلة
        'matplotlib',
        'numpy',
        'pandas',
        'reportlab',
        'PIL',
        'Pillow',
        'cryptography',
        'psutil',
        'xlsxwriter',
        'xlrd',
        'openpyxl',
        'fontTools',
        'win32print',
        'win32api',
        'win32con',
        'win32gui',
        'pywintypes',
        'pythoncom',
        'win32com',
        'tkinter',
        'turtle',
        'test',
        'unittest',
        'distutils',
        'setuptools',
        'pip',
        'wheel',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='المعين_في_الحراسة_العامة_PDF_خفيف',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=False,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='01.ico',
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=False,
    upx_exclude=[],
    name='taheri10_simple'
)
