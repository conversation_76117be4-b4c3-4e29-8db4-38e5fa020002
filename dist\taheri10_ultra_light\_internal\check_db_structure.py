import sqlite3
import os
import sys

def list_tables(db_path: str):
    """
    يعيد قائمة بأسماء كل الجداول في قاعدة البيانات.
    """
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    cursor.execute("""
        SELECT name 
        FROM sqlite_master 
        WHERE type='table' 
        ORDER BY name;
    """)
    tables = [row[0] for row in cursor.fetchall()]
    conn.close()
    return tables

def show_table_structure(db_path: str, table_name: str):
    """
    يعرض بنية الجدول (الأعمدة وأنواعها والقيود الأساسية).
    """
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    cursor.execute(f"PRAGMA table_info({table_name});")
    columns = cursor.fetchall()  # (cid, name, type, notnull, dflt_value, pk)

    if not columns:
        print(f"⚠️ الجدول '{table_name}' غير موجود أو لا يحتوي أعمدة.")
    else:
        print(f"\n-- بنية جدول '{table_name}' --")
        for cid, name, typ, notnull, dflt, pk in columns:
            line = f"• {name} ({typ})"
            if pk:
                line += " [PRIMARY KEY]"
            if notnull:
                line += " NOT NULL"
            if dflt is not None:
                line += f" DEFAULT {dflt}"
            print(line)
    conn.close()

def main():
    # 1. مسار القاعدة تلقائيًا من مجلد السكربت
    script_dir = os.path.dirname(os.path.abspath(__file__))
    db_filename = "data.db"
    db_path = os.path.join(script_dir, db_filename)

    if not os.path.exists(db_path):
        print(f"❌ لم أجد ملف قاعدة البيانات: {db_path!r}")
        sys.exit(1)

    # 2. جلب قائمة الجداول
    tables = list_tables(db_path)
    if not tables:
        print("❌ لا توجد جداول في قاعدة البيانات.")
        sys.exit(0)

    # 3. عرض الجداول للمستخدم
    print("\nالجداول الموجودة في قاعدة البيانات:")
    for idx, tbl in enumerate(tables, start=1):
        print(f"  {idx}. {tbl}")

    # 4. اختيار الجدول
    sel = input("\nأدخل رقم الجدول الذي تريد فحصه: ").strip()
    try:
        sel_idx = int(sel) - 1
        if sel_idx < 0 or sel_idx >= len(tables):
            raise ValueError
    except ValueError:
        print("❌ إدخال غير صالح. شغّل البرنامج مرة أخرى واختر رقم صحيح.")
        sys.exit(1)
    table_name = tables[sel_idx]

    # 5. عرض بنية الجدول المختار
    show_table_structure(db_path, table_name)

if __name__ == "__main__":
    main()
