#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
تقرير واجبات التسجيل لجميع الأقسام
===================================

التحديثات المطبقة:
- الاعتماد على جدول monthly_duties فقط لجلب واجبات التسجيل
- إحصائيات الذكور والإناث تعتمد على عمود الجنس من جدول monthly_duties
- عرض البيانات بالأقسام بدلاً من التلاميذ
- فلترة السجلات حسب النوع LIKE '%تسجيل%'
"""

import os
import sys
import sqlite3
import traceback
from datetime import datetime

def get_database_path():
    """
    الحصول على مسار قاعدة البيانات الصحيح
    يعمل في البيئة العادية وبعد التحزيم
    """
    if getattr(sys, 'frozen', False):
        # البرنامج محزم - البحث في مجلد البرنامج الرئيسي
        application_path = os.path.dirname(sys.executable)
    else:
        # البرنامج يعمل من المصدر
        application_path = os.path.dirname(os.path.abspath(__file__))

    db_path = os.path.join(application_path, 'data.db')

    # التحقق من وجود قاعدة البيانات
    if not os.path.exists(db_path):
        # محاولة البحث في مجلد أعلى
        parent_path = os.path.dirname(application_path)
        alternative_db_path = os.path.join(parent_path, 'data.db')
        if os.path.exists(alternative_db_path):
            return alternative_db_path

    return db_path
import subprocess

# إعدادات التقرير
COL_WIDTHS_TABLE1 = [50, 50, 50, 40]  # جدول معلومات عامة
COL_WIDTHS_TABLE2 = [30, 25, 25, 25, 30, 40, 15]  # جدول الأقسام
COL_WIDTHS_TABLE3 = [60, 60, 70]  # جدول المجاميع

TABLE1_HEADERS = ['القيمة', 'البيان', 'القيمة', 'البيان']
TABLE2_HEADERS = ['إجمالي المحصل', 'عدد الإناث', 'عدد الذكور', 'إجمالي التلاميذ', 'المادة', 'القسم', 'الرقم']
TABLE3_HEADERS = ['النسبة المئوية', 'المبلغ', 'البيان']

ROW_HEIGHT_TABLE1 = 8
ROW_HEIGHT_TABLE2 = 7
ROW_HEIGHT_HEADER = 10

PAGE_MARGIN_TOP = 0.2
PAGE_MARGIN_BOTTOM = 0.2
PAGE_MARGIN_LEFT = 10
PAGE_MARGIN_RIGHT = 10

try:
    from fpdf import FPDF
    import arabic_reshaper
    from bidi.algorithm import get_display
except ImportError:
    subprocess.check_call([sys.executable, "-m", "pip", "install", "fpdf2", "arabic-reshaper", "python-bidi"])
    from fpdf import FPDF
    import arabic_reshaper
    from bidi.algorithm import get_display

class ArabicPDF(FPDF):
    def __init__(self):
        super().__init__('P','mm','A4')
        self.set_margins(PAGE_MARGIN_LEFT, PAGE_MARGIN_TOP, PAGE_MARGIN_RIGHT)
        self.set_auto_page_break(auto=True, margin=PAGE_MARGIN_BOTTOM)
        fonts_dir = os.path.join(os.path.dirname(__file__), 'fonts')
        
        # إضافة خطوط Calibri إذا كانت متوفرة
        calibri_path = os.path.join(fonts_dir, 'calibri.ttf')
        calibri_bold_path = os.path.join(fonts_dir, 'calibrib.ttf')
        
        if os.path.exists(calibri_path):
            self.add_font('Calibri', '', calibri_path)
            self.calibri_available = True
        else:
            self.calibri_available = False
            
        if os.path.exists(calibri_bold_path):
            self.add_font('Calibri', 'B', calibri_bold_path)
            self.calibri_bold_available = True
        else:
            self.calibri_bold_available = False
        
        # تعيين الخط الافتراضي
        if self.calibri_available:
            self.set_font('Calibri', '', 13)
        else:
            self.set_font('Arial', '', 13)
        self.set_line_width(0.4)

    def set_title_font(self, size=15):
        """تعيين خط العناوين"""
        if self.calibri_bold_available:
            self.set_font('Calibri', 'B', size)
        else:
            self.set_font('Arial', 'B', size)

    def set_detail_font(self, size=13):
        """تعيين خط التفاصيل"""
        if self.calibri_bold_available:
            self.set_font('Calibri', 'B', size)
        else:
            self.set_font('Arial', 'B', size)

    def set_normal_font(self, size=12):
        """تعيين الخط العادي"""
        if self.calibri_available:
            self.set_font('Calibri', '', size)
        else:
            self.set_font('Arial', '', size)

    def ar_text(self, txt: str) -> str:
        """تحويل النص العربي ليتم عرضه بشكل صحيح"""
        reshaped = arabic_reshaper.reshape(str(txt))
        return get_display(reshaped)

def get_all_sections_registration_data(db_path):
    """جلب بيانات واجبات التسجيل لجميع الأقسام من جدول monthly_duties"""
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        print(f"🔍 جلب بيانات واجبات التسجيل لجميع الأقسام من جدول monthly_duties")

        # جلب البيانات مجمعة حسب القسم
        cursor.execute("""
            SELECT 
                القسم,
                المادة,
                COUNT(DISTINCT student_id) as total_students,
                COUNT(DISTINCT CASE WHEN الجنس = 'ذكر' THEN student_id END) as male_count,
                COUNT(DISTINCT CASE WHEN الجنس = 'أنثى' THEN student_id END) as female_count,
                SUM(COALESCE(مدفوع_التسجيل, 0)) as total_collected
            FROM monthly_duties
            WHERE النوع LIKE '%تسجيل%'
            AND مدفوع_التسجيل > 0
            GROUP BY القسم, المادة
            ORDER BY القسم, المادة
        """)

        sections_data = cursor.fetchall()
        
        # جلب الإحصائيات العامة
        cursor.execute("""
            SELECT 
                COUNT(DISTINCT القسم) as total_sections,
                COUNT(DISTINCT student_id) as total_students,
                COUNT(DISTINCT CASE WHEN الجنس = 'ذكر' THEN student_id END) as total_males,
                COUNT(DISTINCT CASE WHEN الجنس = 'أنثى' THEN student_id END) as total_females,
                SUM(COALESCE(مدفوع_التسجيل, 0)) as grand_total
            FROM monthly_duties
            WHERE النوع LIKE '%تسجيل%'
            AND مدفوع_التسجيل > 0
        """)

        general_stats = cursor.fetchone()
        conn.close()

        print(f"✅ تم جلب {len(sections_data)} قسم من جدول monthly_duties")
        return sections_data, general_stats

    except Exception as e:
        print(f"خطأ في جلب بيانات واجبات التسجيل: {str(e)}")
        return [], (0, 0, 0, 0, 0)

def generate_all_sections_registration_report(logo_path, sections_data, general_stats, output_path):
    """إنشاء تقرير واجبات التسجيل لجميع الأقسام"""
    pdf = ArabicPDF()
    margin = 10
    usable_w = pdf.w - 2 * margin

    pdf.add_page()
    y = pdf.get_y()

    # إضافة الشعار إذا كان متوفراً
    if logo_path and os.path.exists(logo_path):
        logo_w, logo_h = 60, 30
        x_logo = (pdf.w - logo_w) / 2
        pdf.image(logo_path, x=x_logo, y=y, w=logo_w, h=logo_h)
        y += logo_h + 5

    # عنوان التقرير
    pdf.set_draw_color(0, 51, 102)
    pdf.set_line_width(0.5)
    FIXED_BOX_HEIGHT = 12

    title_text = f"تقرير واجبات التسجيل - جميع الأقسام"

    pdf.set_text_color(0, 51, 102)
    pdf.set_xy(margin, y)
    pdf.set_title_font(15)
    pdf.cell(usable_w, FIXED_BOX_HEIGHT, pdf.ar_text(title_text), border=1, align='C')

    pdf.set_text_color(0, 0, 0)
    y += FIXED_BOX_HEIGHT + 5

    # الجدول الأول: الإحصائيات العامة
    pdf.set_title_font(14)
    pdf.set_text_color(0, 100, 0)
    pdf.set_xy(margin, y)
    pdf.cell(usable_w, 8, pdf.ar_text('الإحصائيات العامة'), border=0, align='C')
    pdf.set_text_color(0, 0, 0)
    y += 10

    cols1 = COL_WIDTHS_TABLE1

    # معلومات إحصائية عامة
    if general_stats:
        section_info_rows = [
            [str(general_stats[1]), 'إجمالي التلاميذ', str(general_stats[0]), 'عدد الأقسام'],
            [str(general_stats[2]), 'عدد الذكور', str(general_stats[3]), 'عدد الإناث'],
            [f'{general_stats[4]:.2f}', 'إجمالي المحصل', datetime.now().strftime('%Y-%m-%d'), 'تاريخ التقرير']
        ]
    else:
        section_info_rows = [
            ['0', 'إجمالي التلاميذ', '0', 'عدد الأقسام'],
            ['0', 'عدد الذكور', '0', 'عدد الإناث'],
            ['0.00', 'إجمالي المحصل', datetime.now().strftime('%Y-%m-%d'), 'تاريخ التقرير']
        ]

    pdf.set_detail_font(13)
    pdf.set_fill_color(230, 240, 255)

    # رسم صفوف الإحصائيات العامة
    for row in section_info_rows:
        x = margin
        for i, cell in enumerate(row):
            pdf.set_xy(x, y)
            fill = i % 2 == 1
            align = 'C' if i % 2 == 1 else 'R'
            pdf.cell(cols1[i], ROW_HEIGHT_TABLE1, pdf.ar_text(cell), border=1, align=align, fill=fill)
            x += cols1[i]
        y += ROW_HEIGHT_TABLE1

    y += 5

    # الجدول الثاني: بيانات الأقسام
    pdf.set_title_font(14)
    pdf.set_text_color(128, 0, 128)
    pdf.set_xy(margin, y)
    pdf.cell(usable_w, 8, pdf.ar_text(f'بيانات واجبات التسجيل حسب الأقسام'), border=0, align='C')
    pdf.set_text_color(0, 0, 0)
    y += 10

    cols2 = COL_WIDTHS_TABLE2

    pdf.set_detail_font(13)
    pdf.set_fill_color(255, 200, 255)

    # رسم رأس الجدول الثاني
    x = margin
    for i, header in enumerate(TABLE2_HEADERS):
        pdf.set_xy(x, y)
        pdf.cell(cols2[i], ROW_HEIGHT_HEADER, pdf.ar_text(header), border=1, align='C', fill=True)
        x += cols2[i]

    y += ROW_HEIGHT_HEADER
    pdf.set_normal_font(11)

    # محتوى جدول الأقسام
    if sections_data:
        for i, section in enumerate(sections_data):
            x = margin
            # ترتيب البيانات: إجمالي المحصل، عدد الإناث، عدد الذكور، إجمالي التلاميذ، المادة، القسم، الرقم
            data = [
                f'{float(section[5]):.2f}' if section[5] else '0.00',  # إجمالي المحصل
                str(section[4]),  # عدد الإناث
                str(section[3]),  # عدد الذكور
                str(section[2]),  # إجمالي التلاميذ
                section[1] or 'غير محدد',  # المادة
                section[0] or 'غير محدد',  # القسم
                str(i + 1)      # الرقم الترتيبي
            ]

            # تلوين متدرج حسب المبلغ المحصل
            amount = float(section[5]) if section[5] else 0
            if amount > 1000:
                pdf.set_fill_color(220, 255, 220)  # أخضر فاتح
            elif amount > 500:
                pdf.set_fill_color(255, 255, 200)  # أصفر فاتح
            else:
                pdf.set_fill_color(255, 220, 220)  # أحمر فاتح

            for j, cell in enumerate(data):
                pdf.set_xy(x, y)
                pdf.cell(cols2[j], ROW_HEIGHT_TABLE2, pdf.ar_text(cell), border=1, align='C', fill=True)
                x += cols2[j]

            y += ROW_HEIGHT_TABLE2

            # الانتقال إلى صفحة جديدة عند الحاجة
            if y > pdf.h - 50:
                pdf.add_page()
                y = pdf.get_y()

                # إضافة الشعار في الصفحة الجديدة
                if logo_path and os.path.exists(logo_path):
                    logo_w, logo_h = 60, 30
                    x_logo = (pdf.w - logo_w) / 2
                    pdf.image(logo_path, x=x_logo, y=y, w=logo_w, h=logo_h)
                    y += logo_h + 5

                # إضافة عنوان التقرير في الصفحة الجديدة
                pdf.set_draw_color(0, 51, 102)
                pdf.set_line_width(0.5)
                FIXED_BOX_HEIGHT = 12

                title_text = f"تقرير واجبات التسجيل - جميع الأقسام"

                pdf.set_text_color(0, 51, 102)
                pdf.set_xy(margin, y)
                pdf.set_title_font(15)
                pdf.cell(usable_w, FIXED_BOX_HEIGHT, pdf.ar_text(title_text), border=1, align='C')

                pdf.set_text_color(0, 0, 0)
                y += FIXED_BOX_HEIGHT + 5

                # إضافة عنوان الجدول الثاني في الصفحة الجديدة
                pdf.set_title_font(14)
                pdf.set_text_color(128, 0, 128)
                pdf.set_xy(margin, y)
                pdf.cell(usable_w, 8, pdf.ar_text(f'بيانات واجبات التسجيل حسب الأقسام'), border=0, align='C')
                pdf.set_text_color(0, 0, 0)
                y += 10

                # إعادة رسم رأس الجدول الثاني في الصفحة الجديدة
                pdf.set_detail_font(13)
                pdf.set_fill_color(255, 200, 255)

                x = margin
                for i, header in enumerate(TABLE2_HEADERS):
                    pdf.set_xy(x, y)
                    pdf.cell(cols2[i], ROW_HEIGHT_HEADER, pdf.ar_text(header), border=1, align='C', fill=True)
                    x += cols2[i]

                y += ROW_HEIGHT_HEADER
                pdf.set_normal_font(11)
    else:
        # إذا لم توجد أقسام
        pdf.set_xy(margin, y)
        pdf.set_fill_color(255, 245, 245)
        pdf.cell(sum(cols2), ROW_HEIGHT_TABLE2, pdf.ar_text('لا توجد بيانات واجبات تسجيل مسجلة'), border=1, align='C', fill=True)
        y += ROW_HEIGHT_TABLE2

    y += 10

    # الجدول الثالث: الملخص النهائي
    pdf.set_title_font(14)
    pdf.set_text_color(0, 100, 100)
    pdf.set_xy(margin, y)
    pdf.cell(usable_w, 8, pdf.ar_text('الملخص النهائي'), border=0, align='C')
    pdf.set_text_color(0, 0, 0)
    y += 10

    cols3 = COL_WIDTHS_TABLE3

    pdf.set_detail_font(13)
    pdf.set_fill_color(200, 255, 200)

    # رسم رأس الجدول الثالث
    x = margin
    for i, header in enumerate(TABLE3_HEADERS):
        pdf.set_xy(x, y)
        pdf.cell(cols3[i], ROW_HEIGHT_HEADER, pdf.ar_text(header), border=1, align='C', fill=True)
        x += cols3[i]

    y += ROW_HEIGHT_HEADER
    pdf.set_normal_font(12)

    # محتوى جدول الملخص النهائي
    if general_stats:
        summary_data = [
            ['-', f'{general_stats[4]:.2f}', 'إجمالي المبلغ المحصل'],
            [f'{general_stats[0]}', '-', 'عدد الأقسام'],
            [f'{general_stats[1]}', '-', 'إجمالي التلاميذ']
        ]
    else:
        summary_data = [
            ['-', '0.00', 'إجمالي المبلغ المحصل'],
            ['0', '-', 'عدد الأقسام'],
            ['0', '-', 'إجمالي التلاميذ']
        ]

    for i, row in enumerate(summary_data):
        x = margin

        # تلوين مختلف لكل صف
        if i == 0:  # المحصل
            pdf.set_fill_color(240, 255, 240)  # أخضر فاتح
        elif i == 1:  # عدد الأقسام
            pdf.set_fill_color(255, 255, 240)  # أصفر فاتح
        else:  # عدد التلاميذ
            pdf.set_fill_color(240, 240, 255)  # أزرق فاتح

        for j, cell in enumerate(row):
            pdf.set_xy(x, y)
            pdf.cell(cols3[j], ROW_HEIGHT_TABLE2, pdf.ar_text(cell), border=1, align='C', fill=True)
            x += cols3[j]

        y += ROW_HEIGHT_TABLE2

    # حفظ الملف
    pdf.output(output_path)
    print(f"تم إنشاء تقرير واجبات التسجيل لجميع الأقسام: {output_path}")

def print_all_sections_registration_report(parent=None):
    """
    دالة لإنشاء تقرير واجبات التسجيل لجميع الأقسام
    """
    try:
        # الحصول على مسار قاعدة البيانات
        db_path = get_database_path()

        # البحث عن ملف الشعار
        logo_path = None
        possible_logo_paths = [
            os.path.join(os.path.dirname(__file__), 'logo.png'),
            os.path.join(os.path.dirname(__file__), 'logo.jpg'),
            os.path.join(os.path.dirname(__file__), 'logo.jpeg'),
            os.path.join(os.path.expanduser('~'), 'Desktop', 'logo.png'),
            os.path.join(os.path.expanduser('~'), 'Desktop', 'logo.jpg'),
            os.path.join(os.path.expanduser('~'), 'Desktop', 'logo.jpeg')
        ]

        for path in possible_logo_paths:
            if os.path.exists(path):
                logo_path = path
                break

        # جلب البيانات من قاعدة البيانات
        try:
            sections_data, general_stats = get_all_sections_registration_data(db_path)

        except Exception as db_error:
            print(f"خطأ في الوصول لقاعدة البيانات: {db_error}")
            return False, None, f"خطأ في الوصول لقاعدة البيانات: {str(db_error)}"

        # إنشاء مجلد التقارير إذا لم يكن موجوداً
        reports_dir = os.path.join(os.path.expanduser('~'), 'Desktop', 'تقارير واجبات التسجيل')
        if not os.path.exists(reports_dir):
            os.makedirs(reports_dir)

        # تحديد اسم الملف
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_path = os.path.join(reports_dir, f"تقرير_واجبات_التسجيل_جميع_الاقسام_{timestamp}.pdf")

        # إنشاء التقرير
        generate_all_sections_registration_report(logo_path, sections_data, general_stats, output_path)

        # فتح الملف بعد إنشائه
        try:
            if sys.platform == 'win32':
                os.startfile(output_path)
            elif sys.platform == 'darwin':  # macOS
                subprocess.call(['open', output_path])
            else:  # Linux
                subprocess.call(['xdg-open', output_path])
        except Exception as e:
            return True, output_path, f"تم إنشاء التقرير ولكن تعذر فتح الملف: {str(e)}"

        return True, output_path, "تم إنشاء تقرير واجبات التسجيل لجميع الأقسام بنجاح."

    except Exception as e:
        traceback.print_exc()
        return False, None, f"حدث خطأ في إنشاء التقرير: {str(e)}"

if __name__=='__main__':
    # مثال على الاستخدام
    try:
        success, output_path, message = print_all_sections_registration_report()

        print(f"النتيجة: {success}")
        print(f"المسار: {output_path}")
        print(f"الرسالة: {message}")

    except Exception as e:
        print(f"خطأ في الاختبار: {e}")
        traceback.print_exc()
