
This file lists modules PyInstaller was not able to find. This does not
necessarily mean this module is required for running your program. Python and
Python 3rd-party packages include a lot of conditional or optional modules. For
example the module 'ntpath' only exists on Windows, whereas the module
'posixpath' only exists on Posix systems.

Types if import:
* top-level: imported at the top-level - look at these first
* conditional: imported within an if-statement
* delayed: imported within a function
* optional: imported within a try-except-statement

IMPORTANT: Do NOT post this list to the issue-tracker. Use it as a basis for
            tracking down the missing module yourself. Thanks!

missing module named grp - imported by shutil (delayed, optional), tarfile (optional), pathlib._local (optional), subprocess (delayed, conditional, optional)
missing module named pwd - imported by posixpath (delayed, conditional, optional), shutil (delayed, optional), tarfile (optional), pathlib._local (optional), subprocess (delayed, conditional, optional), netrc (delayed, conditional), getpass (delayed, optional), http.server (delayed, optional)
missing module named posix - imported by posixpath (optional), shutil (conditional), importlib._bootstrap_external (conditional), os (conditional, optional)
missing module named resource - imported by posix (top-level)
missing module named _frozen_importlib_external - imported by importlib._bootstrap (delayed), importlib (optional), importlib.abc (optional), zipimport (top-level)
excluded module named _frozen_importlib - imported by importlib (optional), importlib.abc (optional), zipimport (top-level)
missing module named 'collections.abc' - imported by traceback (top-level), typing (top-level), inspect (top-level), logging (top-level), importlib.resources.readers (top-level), selectors (top-level), tracemalloc (top-level), sqlite3.dbapi2 (top-level), fpdf.drawing (top-level), http.client (top-level), xml.etree.ElementTree (top-level), configparser (top-level)
missing module named pyimod02_importers - imported by C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\PyInstaller\hooks\rthooks\pyi_rth_pkgutil.py (delayed)
missing module named 'fontTools.ttLib' - imported by arabic_reshaper.reshaper_config (optional)
excluded module named PIL - imported by fpdf.util (delayed), fpdf.image_parsing (optional)
missing module named pympler - imported by fpdf.util (delayed, optional)
missing module named pymemtrace - imported by fpdf.util (delayed, optional)
missing module named uharfbuzz - imported by fpdf.fonts (optional), fpdf.fpdf (delayed, optional)
missing module named _scproxy - imported by urllib.request (conditional)
missing module named termios - imported by getpass (optional), tty (top-level), _pyrepl.pager (delayed, optional)
missing module named xmlrpclib - imported by defusedxml.xmlrpc (conditional)
missing module named fcntl - imported by subprocess (optional), xmlrpc.server (optional)
missing module named _posixsubprocess - imported by subprocess (conditional)
missing module named vms_lib - imported by platform (delayed, optional)
missing module named 'java.lang' - imported by platform (delayed, optional)
missing module named java - imported by platform (delayed)
missing module named 'fontTools.pens' - imported by fpdf.fonts (top-level), fpdf.svg (top-level)
missing module named 'fontTools.svgLib' - imported by fpdf.svg (top-level)
missing module named 'unittest.mock' - imported by fpdf.sign (top-level)
excluded module named fontTools - imported by fpdf.fonts (top-level), fpdf.output (top-level)
missing module named 'PIL.Image' - imported by fpdf.fpdf (optional), fpdf.image_parsing (optional)
missing module named endesive - imported by fpdf.fpdf (optional), fpdf.output (optional), fpdf.linearization (optional)
missing module named 'cryptography.hazmat' - imported by fpdf.fpdf (optional), fpdf.encryption (optional)
excluded module named numpy - imported by cash_flow_window (optional)
missing module named 'matplotlib.backends' - imported by cash_flow_window (optional)
missing module named 'matplotlib.pyplot' - imported by cash_flow_window (optional)
excluded module named win32print - imported by sub3_window (top-level)
excluded module named openpyxl - imported by sub8_window (delayed, conditional, optional)
excluded module named pandas - imported by sub8_window (optional)
missing module named readline - imported by code (delayed, conditional, optional), sqlite3.__main__ (delayed, conditional, optional)
