// Licensed to the Apache Software Foundation (ASF) under one
// or more contributor license agreements.  See the NOTICE file
// distributed with this work for additional information
// regarding copyright ownership.  The ASF licenses this file
// to you under the Apache License, Version 2.0 (the
// "License"); you may not use this file except in compliance
// with the License.  You may obtain a copy of the License at
//
//   http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing,
// software distributed under the License is distributed on an
// "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
// KIND, either express or implied.  See the License for the
// specific language governing permissions and limitations
// under the License.

#pragma once

#include "arrow/util/endian.h"
#include "arrow/util/visibility.h"

#include <stdint.h>

namespace arrow {
namespace internal {

ARROW_EXPORT
int unpack32(const uint32_t* in, uint32_t* out, int batch_size, int num_bits);
ARROW_EXPORT
int unpack64(const uint8_t* in, uint64_t* out, int batch_size, int num_bits);

}  // namespace internal
}  // namespace arrow
