# -*- mode: python ; coding: utf-8 -*-
"""
ملف تحزيم محسّن للغاية - تم تقليل الحجم بشكل كبير
====================================================

آخر تحديث: 2025-08-28
التحسينات المطبقة:
- إزالة جميع المكتبات غير المستخدمة فعلياً
- الاحتفاظ بالمكتبات الأساسية فقط: PyQt5, fpdf, arabic_reshaper, bidi, sqlite3
- إزالة ملفات البيانات غير الضرورية
- تحسين قائمة الاستبعاد لتقليل الحجم
- إزالة المجلدات الفارغة وملفات السجلات
"""

import os
block_cipher = None

# ملفات البيانات الأساسية فقط - تم تقليلها بشكل كبير
datas=[
    # الأيقونة الأساسية
    ('01.ico', '.'),

    # ملفات النوافذ الموجودة فعلياً فقط
    ('main_window.py', '.'),
    ('sub01_window.py', '.'),
    ('sub2_window.py', '.'),
    ('sub8_window.py', '.'),
    ('sub252_window.py', '.'),
    ('sub262_window.py', '.'),
    ('sub263_window.py', '.'),
    ('sub26662_window.py', '.'),
    ('sub3_window.py', '.'),
    ('sub4_window.py', '.'),
    ('sub100_window.py', '.'),
    ('expense_management_window.py', '.'),
    ('cash_flow_window.py', '.'),
    ('financial_system_launcher.py', '.'),
    ('monthly_duties_window.py', '.'),
    ('attendance_processing_window.py', '.'),
    ('student_multi_registration.py', '.'),
    ('run_student_registration.py', '.'),
    ('check_db_structure.py', '.'),

    # ملفات الطباعة الأساسية فقط
    ('print101.py', '.'),
    ('print111.py', '.'),
    ('print144.py', '.'),
    ('print_registration_fees.py', '.'),
    ('print_registration_fees_monthly_style.py', '.'),
    ('print_registration_fees_simple.py', '.'),
    ('print_registration_fees_all_sections.py', '.'),
    ('print1_section_monthly.py', '.'),
    ('print_section_monthly.py', '.'),
    ('print_section_yearly.py', '.'),
    ('attendance_sheet_report.py', '.'),
    ('daily_attendance_sheet_report.py', '.'),

    # قاعدة البيانات الأساسية
    ('data.db', '.'),

    # الخطوط الأساسية فقط (بدون مجلدات فارغة)
    ('fonts/Arial.ttf', 'fonts/'),
    ('fonts/arialbd.ttf', 'fonts/'),
    ('fonts/calibri.ttf', 'fonts/'),
    ('fonts/calibrib.ttf', 'fonts/'),
]

a = Analysis(
    ['main_window.py'],
    pathex=[r'c:\Users\<USER>\Desktop\csv\taheri10'],
    binaries=[],
    datas=datas,
    hiddenimports=[
        # النوافذ الأساسية فقط
        'main_window',
        'sub01_window',
        'sub2_window',
        'sub8_window',
        'sub252_window',
        'sub262_window',
        'sub263_window',
        'sub26662_window',
        'sub3_window',
        'sub4_window',
        'sub100_window',
        'expense_management_window',
        'cash_flow_window',
        'financial_system_launcher',
        'monthly_duties_window',
        'attendance_processing_window',
        'student_multi_registration',
        'run_student_registration',
        'check_db_structure',

        # وحدات الطباعة الأساسية فقط
        'print101',
        'print111',
        'print144',
        'print_registration_fees',
        'print_registration_fees_monthly_style',
        'print_registration_fees_simple',
        'print_registration_fees_all_sections',
        'print1_section_monthly',
        'print_section_monthly',
        'print_section_yearly',
        'attendance_sheet_report',
        'daily_attendance_sheet_report',

        # مكتبات PDF الأساسية فقط
        'fpdf',
        'fpdf.fpdf',

        # مكتبات النصوص العربية الأساسية فقط
        'arabic_reshaper',
        'bidi',
        'bidi.algorithm',

        # مكتبات PyQt5 الأساسية فقط
        'PyQt5',
        'PyQt5.QtWidgets',
        'PyQt5.QtGui',
        'PyQt5.QtCore',
        'PyQt5.sip',

        # مكتبات النظام الأساسية فقط
        'sqlite3',
        'datetime',
        'os',
        'sys',
        'csv',
        'logging',
        'traceback',
        'subprocess',

        # مكتبات الترميز الأساسية فقط
        'encodings',
        'encodings.utf_8',
        'encodings.cp1256',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        # استبعاد المكتبات الثقيلة غير المستخدمة
        'matplotlib',
        'numpy',
        'pandas',
        'reportlab',
        'PIL',
        'Pillow',
        'cryptography',
        'psutil',
        'xlsxwriter',
        'xlrd',
        'dateutil',
        'fontTools',
        'openpyxl',

        # استبعاد مكتبات Windows غير المستخدمة
        'win32print',
        'win32api',
        'win32con',
        'win32gui',
        'win32file',
        'pywintypes',
        'pythoncom',
        'win32com',

        # استبعاد مكتبات التطوير والاختبار
        'tkinter',
        'turtle',
        'test',
        'unittest',
        'doctest',
        'distutils',
        'setuptools',
        'pip',
        'wheel',
        'pkg_resources',

        # استبعاد مكتبات أخرى غير مستخدمة
        'multiprocessing',
        'concurrent',
        'asyncio',
        'xml',
        'json',
        'pickle',
        'base64',
        'zlib',
        'gzip',
        'zipfile',
        'tarfile',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

# وضع "one-folder" (مجلد واحد) - أكثر أماناً
exe = EXE(
    pyz,
    a.scripts,
    [],  # فارغة لوضع المجلد الواحد
    exclude_binaries=True,  # مهم! يجب أن تكون True للمجلد الواحد
    name='المعين_في_الحراسة_العامة_PDF_نهائي',
    debug=False,  # إيقاف وضع التصحيح
    bootloader_ignore_signals=False,
    strip=False,  # إيقاف strip لتجنب مشاكل الفيروسات
    upx=False,   # إيقاف UPX لتجنب اكتشاف الفيروسات الوهمية
    console=False,  # إخفاء نافذة موجه الأوامر
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,  # للتوافق مع النظام الحالي
    codesign_identity=None,
    entitlements_file=None,
    icon='01.ico',
    version=None,  # إزالة معلومات الإصدار المعقدة
)

# جمع جميع الملفات في مجلد واحد - أكثر أماناً
coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,  # إيقاف strip
    upx=False,    # إيقاف UPX
    upx_exclude=[],
    name='taheri10'
)
