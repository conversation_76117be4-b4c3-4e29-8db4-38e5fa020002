# -*- mode: python ; coding: utf-8 -*-
"""
ملف تحزيم محسّن للغاية - تم تقليل الحجم بشكل كبير
====================================================

آخر تحديث: 2025-08-28
التحسينات المطبقة:
- إزالة جميع المكتبات غير المستخدمة فعلياً
- الاحتفاظ بالمكتبات الأساسية فقط: PyQt5, fpdf, arabic_reshaper, bidi, sqlite3
- إزالة ملفات البيانات غير الضرورية
- تحسين قائمة الاستبعاد لتقليل الحجم
- إزالة المجلدات الفارغة وملفات السجلات
"""

import os
import sys
block_cipher = None

# ملفات البيانات الأساسية فقط - تم تقليلها للحد الأدنى
datas=[
    # الأيقونة الأساسية فقط
    ('01.ico', '.'),

    # قاعدة البيانات الأساسية فقط
    ('data.db', '.'),

    # الخطوط الأساسية فقط (خطين فقط لتوفير المساحة)
    ('fonts/Arial.ttf', 'fonts/'),
    ('fonts/arialbd.ttf', 'fonts/'),
]

# ملاحظة مهمة: تم إزالة ملفات .py من datas لأن PyInstaller يجمعها تلقائياً
# هذا يوفر مساحة كبيرة ويمنع التكرار

a = Analysis(
    ['main_window.py'],
    pathex=[r'c:\Users\<USER>\Desktop\csv\taheri10'],
    binaries=[],
    datas=datas,
    hiddenimports=[
        # النوافذ الأساسية فقط
        'main_window',
        'sub01_window',
        'sub2_window',
        'sub8_window',
        'sub252_window',
        'sub262_window',
        'sub263_window',
        'sub26662_window',
        'sub3_window',
        'sub4_window',
        'sub100_window',
        'expense_management_window',
        'cash_flow_window',
        'financial_system_launcher',
        'monthly_duties_window',
        'attendance_processing_window',
        'student_multi_registration',
        'run_student_registration',
        'check_db_structure',

        # وحدات الطباعة الأساسية فقط
        'print101',
        'print111',
        'print144',
        'print_registration_fees',
        'print_registration_fees_monthly_style',
        'print_registration_fees_simple',
        'print_registration_fees_all_sections',
        'print1_section_monthly',
        'print_section_monthly',
        'print_section_yearly',
        'attendance_sheet_report',
        'daily_attendance_sheet_report',

        # مكتبات PDF الأساسية فقط
        'fpdf',
        'fpdf.fpdf',

        # مكتبات النصوص العربية الأساسية فقط
        'arabic_reshaper',
        'bidi',
        'bidi.algorithm',

        # مكتبة الخطوط الضرورية (الحد الأدنى)
        'fontTools',
        'fontTools.ttLib',

        # مكتبات PyQt5 الأساسية فقط
        'PyQt5',
        'PyQt5.QtWidgets',
        'PyQt5.QtGui',
        'PyQt5.QtCore',
        'PyQt5.sip',

        # مكتبات النظام الأساسية فقط (تم تقليلها)
        'sqlite3',
        '_sqlite3',

        # مكتبات الترميز الأساسية فقط
        'encodings.utf_8',
        'encodings.cp1256',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        # استبعاد المكتبات الثقيلة غير المستخدمة
        'matplotlib',
        'numpy',
        'pandas',
        'reportlab',
        'PIL',
        'Pillow',
        'cryptography',
        'psutil',
        'xlsxwriter',
        'xlrd',
        'dateutil',
        'openpyxl',

        # استبعاد مكتبات Windows غير المستخدمة
        'win32print',
        'win32api',
        'win32con',
        'win32gui',
        'win32file',
        'pywintypes',
        'pythoncom',
        'win32com',

        # استبعاد مكتبات التطوير والاختبار
        'tkinter',
        'turtle',
        'test',
        'unittest',
        'doctest',
        'distutils',
        'setuptools',
        'pip',
        'wheel',
        'pkg_resources',

        # استبعاد مكتبات أخرى غير مستخدمة
        'multiprocessing',
        'concurrent',
        'asyncio',
        'xml',
        'pickle',
        'base64',
        'gzip',
        'zipfile',
        'tarfile',
        'email',
        'urllib',
        'http',
        'ssl',
        'socket',
        'threading',
        'queue',
        'collections',
        'itertools',
        'functools',
        'operator',
        'weakref',
        'copy',
        'pprint',
        'textwrap',
        'difflib',
        'stringprep',
        'locale',
        'calendar',
        'hashlib',
        'hmac',
        'secrets',
        'uuid',
        'random',
        'statistics',
        'decimal',
        'fractions',
        'math',
        'cmath',
        'numbers',
        'array',
        'struct',
        'codecs',
        'unicodedata',
        'string',
        're',
        'pathlib',
        'glob',
        'fnmatch',
        'linecache',
        'shutil',
        'tempfile',
        'filecmp',
        'stat',
        'platform',
        'ctypes',
        'mmap',
        'resource',
        'gc',
        'sys',
        'os',
        'io',
        'time',
        'datetime',
        'zoneinfo',
        'argparse',
        'getopt',
        'logging',
        'getpass',
        'curses',
        'readline',
        'rlcompleter',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

# وضع "one-file" (ملف واحد) - أصغر حجماً وأسهل في التوزيع
exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='المعين_في_الحراسة_العامة_PDF_خفيف_جداً',
    debug=False,
    bootloader_ignore_signals=False,
    strip=True,    # تفعيل strip لتقليل الحجم
    upx=True,      # تفعيل UPX لضغط أفضل
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='01.ico',
)
