@echo off
echo ========================================
echo تحزيم التطبيق - الإصدار المحسن
echo ========================================
echo.

echo تنظيف الملفات القديمة...
if exist "build" rmdir /s /q "build"
if exist "dist" rmdir /s /q "dist"
if exist "taheri10_simple" rmdir /s /q "taheri10_simple"
if exist "taheri10_ultra_light" rmdir /s /q "taheri10_ultra_light"

echo.
echo بدء عملية التحزيم...
echo استخدام الملف المبسط: ultimate_pdf_build_simple.spec
echo.

pyinstaller ultimate_pdf_build_simple.spec

echo.
if exist "dist\taheri10_simple" (
    echo ✅ تم التحزيم بنجاح!
    echo المجلد: dist\taheri10_simple
    echo.
    echo فتح مجلد النتائج...
    explorer "dist\taheri10_simple"
) else (
    echo ❌ فشل في التحزيم
    echo تحقق من الأخطاء أعلاه
)

echo.
echo اضغط أي مفتاح للإغلاق...
pause >nul
