@echo off
setlocal enabledelayedexpansion
echo ========================================
echo تحزيم التطبيق - الإصدار المحسن للغاية
echo ========================================
echo.

echo تنظيف الملفات القديمة...
if exist "build" rmdir /s /q "build"
if exist "dist" rmdir /s /q "dist"

echo.
echo اختر نوع التحزيم:
echo 1. أصغر حجم ممكن (مُوصى به) - ultimate_pdf_build_minimal.spec
echo 2. الملف المحسن - ultimate_pdf_build.spec
echo 3. تشغيل سريع بدون اختيار
echo.
set /p choice="اختر رقم (1-3): "

if "%choice%"=="1" (
    echo استخدام الملف بأصغر حجم...
    pyinstaller ultimate_pdf_build_minimal.spec
    set "result_file=المعين_في_الحراسة_العامة_PDF_أصغر_حجم.exe"
    set "result_type=file"
) else if "%choice%"=="2" (
    echo استخدام الملف المحسن...
    pyinstaller ultimate_pdf_build.spec
    set "result_file=المعين_في_الحراسة_العامة_PDF_خفيف_جداً.exe"
    set "result_type=file"
) else if "%choice%"=="3" (
    echo تشغيل سريع - استخدام أصغر حجم...
    pyinstaller ultimate_pdf_build_minimal.spec
    set "result_file=المعين_في_الحراسة_العامة_PDF_أصغر_حجم.exe"
    set "result_type=file"
) else (
    echo خيار غير صحيح، استخدام أصغر حجم...
    pyinstaller ultimate_pdf_build_minimal.spec
    set "result_file=المعين_في_الحراسة_العامة_PDF_أصغر_حجم.exe"
    set "result_type=file"
)

echo.
if exist "dist\%result_file%" (
    echo ✅ تم التحزيم بنجاح!
    echo الملف: dist\%result_file%

    REM حساب حجم الملف
    for %%A in ("dist\%result_file%") do (
        set size=%%~zA
        set /a sizeMB=!size!/1024/1024
    )
    echo حجم الملف: !sizeMB! ميجابايت تقريباً
    echo.
    echo فتح مجلد النتائج...
    explorer "dist"
) else (
    echo ❌ فشل في التحزيم
    echo تحقق من الأخطاء أعلاه
)

echo.
echo اضغط أي مفتاح للإغلاق...
pause >nul
